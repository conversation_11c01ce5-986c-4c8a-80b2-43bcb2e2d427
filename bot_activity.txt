2025-07-30 17:49:03 - INFO - Database initialized successfully
2025-07-30 17:49:04 - INFO - [PerformanceMonitor] Initialized with window_size=100, enabled=True
2025-07-30 17:49:04 - INFO - [ResourceManager] Initialized with cache_ttl=3600s, cleanup_interval=300s
2025-07-30 17:49:04 - INFO - ThreadPoolManager initialized with max_workers=16, cleanup_interval=120s
2025-07-30 17:49:04 - INFO - [GenAIClientManager] Initialized with 69 API keys and performance optimizations
2025-07-30 17:49:04 - INFO - Loaded bot data: 0 admins, 0 blocked users, 0 scheduled podcasts, 0 pro users, VEO enabled: True
2025-07-30 17:49:04 - INFO - Initializing bot_data.json with complete structure...
2025-07-30 17:49:04 - INFO - Loaded 0 group model settings
2025-07-30 17:49:04 - INFO - [PodcastWorker-PodcastWorker-0] Started
2025-07-30 17:49:04 - INFO - [PodcastWorker-PodcastWorker-1] Started
2025-07-30 17:49:04 - INFO - [PodcastWorker-PodcastWorker-2] Started
2025-07-30 17:49:04 - INFO - [PodcastWorker-PodcastWorker-3] Started
2025-07-30 17:49:04 - INFO - [PodcastWorker-PodcastWorker-4] Started
2025-07-30 17:49:04 - INFO - [PodcastWorker-PodcastWorker-5] Started
2025-07-30 17:49:04 - INFO - [PodcastWorker-PodcastWorker-6] Started
2025-07-30 17:49:04 - INFO - [PodcastWorker-PodcastWorker-7] Started
2025-07-30 17:49:04 - INFO - Initialized podcast queue with 8 workers (reduced from 50 for stability)
2025-07-30 17:49:04 - INFO - HTML Group: Module initialized
2025-07-30 17:49:04 - INFO - HTML Group: Module ready for use with random key selection!
2025-07-30 17:49:04 - WARNING - 🚀 Запуск бота через start_bot()...
2025-07-30 17:49:04 - INFO - Checking for ffmpeg...
2025-07-30 17:49:04 - INFO - ffmpeg found.
2025-07-30 17:49:04 - INFO - Initializing telegra.ph client...
2025-07-30 17:49:05 - INFO - API SUCCESS: Информация о боте получена за 0.50s: @UseShBot (ID: **********) [попытка 1]
2025-07-30 17:49:05 - INFO - loaded telegra.ph token from .telegraph_token
2025-07-30 17:49:05 - INFO - telegra.ph client initialized for account: UseShBot
2025-07-30 17:49:05 - INFO - telegra.ph client ready.
2025-07-30 17:49:05 - INFO - Загрузка настроек пользователей...
2025-07-30 17:49:05 - INFO - Файл настроек пользователей не найден. Будут использованы настройки по умолчанию.
2025-07-30 17:49:05 - INFO - Используются настройки пользователей по умолчанию.
2025-07-30 17:49:05 - INFO - Инициализация базы данных...
2025-07-30 17:49:05 - INFO - Database initialized successfully
2025-07-30 17:49:05 - INFO - База данных успешно инициализирована.
2025-07-30 17:49:05 - INFO - Unified background scheduler started
2025-07-30 17:49:05 - INFO - Unified background scheduler thread started (оптимизированный).
2025-07-30 17:49:05 - INFO - Bot sh is starting...
2025-07-30 17:49:05 - INFO - MAIN: Bot polling starting...
2025-07-30 17:49:08 - INFO - 🛑 Получен сигнал остановки бота
2025-07-30 17:49:08 - WARNING - ⚠️ Polling завершен
2025-07-30 17:49:08 - CRITICAL - Цикл polling завершен или прерван
2025-07-30 17:49:08 - INFO - Stopping bot...
2025-07-30 17:49:08 - INFO - Shutting down podcast workers...
2025-07-30 17:49:10 - INFO - 🔥 Запуск разогрева соединений для быстрого отклика...
2025-07-30 17:49:10 - INFO - [GenAI] Starting connection warmup for 2 keys
2025-07-30 17:49:12 - WARNING - [GenAI] Warmup failed for key ...kqts: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key not valid. Please pass a valid API
2025-07-30 17:49:12 - INFO - [GenAI] Connection warmup completed: 1/2 successful
2025-07-30 17:49:12 - INFO - ✅ Разогрев завершен: 1 соединений готовы
2025-07-30 17:49:34 - INFO - [PodcastWorker-PodcastWorker-7] Shutdown
2025-07-30 17:49:34 - INFO - [PodcastWorker-PodcastWorker-5] Shutdown
2025-07-30 17:49:34 - INFO - [PodcastWorker-PodcastWorker-1] Shutdown
2025-07-30 17:49:34 - INFO - [PodcastWorker-PodcastWorker-3] Shutdown
2025-07-30 17:49:34 - INFO - [PodcastWorker-PodcastWorker-2] Shutdown
2025-07-30 17:49:34 - INFO - [PodcastWorker-PodcastWorker-0] Shutdown
2025-07-30 17:49:34 - INFO - [PodcastWorker-PodcastWorker-4] Shutdown
2025-07-30 17:49:34 - INFO - [PodcastWorker-PodcastWorker-6] Shutdown
2025-07-30 17:49:34 - INFO - Podcast workers shutdown complete
2025-07-30 17:49:34 - INFO - ThreadPoolManager shutdown complete
2025-07-30 17:49:34 - INFO - Сохранение настроек пользователей перед завершением...
2025-07-30 17:49:34 - INFO - Настройки пользователей успешно сохранены.
2025-07-30 17:49:34 - WARNING - ✅ Бот остановлен
2025-07-31 17:33:39 - INFO - HTML Group: Module initialized
2025-07-31 17:33:39 - INFO - HTML Group: Module ready with GPT-5 as primary for /html
2025-08-01 17:08:53 - INFO - Database initialized successfully
2025-08-01 17:08:53 - INFO - [PerformanceMonitor] Initialized with window_size=100, enabled=True
2025-08-01 17:08:53 - INFO - [ResourceManager] Initialized with cache_ttl=3600s, cleanup_interval=300s
2025-08-01 17:08:53 - INFO - ThreadPoolManager initialized with max_workers=16, cleanup_interval=120s
2025-08-01 17:08:53 - INFO - [GenAIClientManager] Initialized with 69 API keys and performance optimizations
2025-08-01 17:08:53 - INFO - Loaded bot data: 0 admins, 0 blocked users, 0 scheduled podcasts, 0 pro users, VEO enabled: True
2025-08-01 17:08:53 - INFO - Loaded 0 group model settings
2025-08-01 17:08:53 - INFO - [PodcastWorker-PodcastWorker-0] Started
2025-08-01 17:08:53 - INFO - [PodcastWorker-PodcastWorker-1] Started
2025-08-01 17:08:53 - INFO - [PodcastWorker-PodcastWorker-2] Started
2025-08-01 17:08:53 - INFO - [PodcastWorker-PodcastWorker-3] Started
2025-08-01 17:08:53 - INFO - [PodcastWorker-PodcastWorker-4] Started
2025-08-01 17:08:53 - INFO - [PodcastWorker-PodcastWorker-5] Started
2025-08-01 17:08:53 - INFO - [PodcastWorker-PodcastWorker-6] Started
2025-08-01 17:08:53 - INFO - [PodcastWorker-PodcastWorker-7] Started
2025-08-01 17:08:53 - INFO - Initialized podcast queue with 8 workers (reduced from 50 for stability)
2025-08-01 17:08:53 - INFO - HTML Group: Module initialized
2025-08-01 17:08:53 - INFO - HTML Group: Module ready with GPT-5 as primary for /html
2025-08-01 17:08:53 - WARNING - 🚀 Запуск бота через start_bot()...
2025-08-01 17:08:53 - INFO - Checking for ffmpeg...
2025-08-01 17:08:53 - INFO - ffmpeg found.
2025-08-01 17:08:53 - INFO - Initializing telegra.ph client...
2025-08-01 17:08:54 - INFO - API SUCCESS: Информация о боте получена за 0.29s: @UseShBot (ID: **********) [попытка 1]
2025-08-01 17:08:54 - INFO - loaded telegra.ph token from .telegraph_token
2025-08-01 17:08:54 - INFO - telegra.ph client initialized for account: UseShBot
2025-08-01 17:08:54 - INFO - telegra.ph client ready.
2025-08-01 17:08:54 - INFO - Загрузка настроек пользователей...
2025-08-01 17:08:54 - INFO - Файл настроек пользователей не найден. Будут использованы настройки по умолчанию.
2025-08-01 17:08:54 - INFO - Используются настройки пользователей по умолчанию.
2025-08-01 17:08:54 - INFO - Инициализация базы данных...
2025-08-01 17:08:54 - INFO - Database initialized successfully
2025-08-01 17:08:54 - INFO - База данных успешно инициализирована.
2025-08-01 17:08:54 - INFO - Unified background scheduler started
2025-08-01 17:08:54 - INFO - Unified background scheduler thread started (оптимизированный).
2025-08-01 17:08:54 - INFO - Bot sh is starting...
2025-08-01 17:08:54 - INFO - MAIN: Bot polling starting...
